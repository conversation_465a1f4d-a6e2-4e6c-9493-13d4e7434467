    # PHP upstream
    upstream php {
        server unix:/var/run/php/php8.3-fpm.sock;
        # Alternative TCP: server 127.0.0.1:9000;
    }

    # Main server block for PHP website
    server {
        listen 80;
        server_name localhost;
        root /var/www/html;
        index index.php index.html index.htm;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";

        # Main location block
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        # PHP processing
        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass php;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;

            # Additional FastCGI params
            fastcgi_param PATH_INFO $fastcgi_path_info;
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;
        }

        # Static files caching
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Deny access to hidden files
        location ~ /\. {
            deny all;
        }

        # Deny access to sensitive files
        location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
            deny all;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
    }
}