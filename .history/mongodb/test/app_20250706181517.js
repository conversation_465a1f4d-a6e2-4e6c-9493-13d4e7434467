const express = require('express');
const { ObjectId } = require('mongodb');
const app = express();

app.use(express.json());

const { connectToDb, getDb } = require('./db');

let db;

connectToDb(error => {
    if (error) {
        console.log(error);
    } else {
        app.listen(3000, () => {
            console.log('Example app listening on port 3000!');
        });
        db = getDb();
    }
});


app.get('/books', (req, res) => {
    let books = [];
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    db.collection('books')
        .find()
        .skip(skip)
        .limit(limit)
        .forEach(book => books.push(book))
        .then(() => res.status(200).json(books))
        .catch(() => res.status(500).json({ error: 'Could not fetch the documents' }));
});


app.get('/books/:id', (req, res) => {
    const id = req.params.id;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ error: 'Invalid id' });
    }
    const objectId = new ObjectId(id);

    db.collection('books').findOne({ _id: objectId })
       .then(book => res.status(200).json(book))
       .catch(() => res.status(500).json({ error: 'Could not fetch the document' }));
});

app.post('/books', (req, res) => {
    const book = req.body;
    db.collection('books').insertOne(book)
        .then(result => res.status(201).json(result))
        .catch(() => res.status(500).json({ error: 'Could not create a new document' }));
});

app.delete('/books/:id', (req, res) => {
    const id = req.params.id;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ error: 'Invalid id' });
    }
    const objectId = new ObjectId(id);

    db.collection('books').deleteOne({ _id: objectId })
        .then(result => res.status(200).json(result))
        .catch(() => res.status(500).json({ error: 'Could not delete the document' }));
});

app.put('/books/:id', (req, res) => {
    const id = req.params.id;
    const updatedBook = req.body;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ error: 'Invalid id' });
    }
    const objectId = new ObjectId(id);

    db.collection('books').updateOne({ _id: objectId }, { $set: updatedBook })
        .then(result => res.status(200).json(result))
        .catch(() => res.status(500).json({ error: 'Could not update the document' }));
})