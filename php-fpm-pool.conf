[www]
; Pool name - you can have multiple pools
user = www-data
group = www-data

; How to listen for requests
; Option 1: Unix socket (recommended for same server)
listen = /var/run/php/php8.2-fpm.sock
; Option 2: TCP socket (use for Docker or remote)
; listen = 127.0.0.1:9000

; Socket permissions (only needed for Unix socket)
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Process management settings
pm = dynamic
pm.max_children = 20
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
pm.max_requests = 1000

; Logging
catch_workers_output = yes
php_admin_value[error_log] = /var/log/php-fpm/www-error.log
php_admin_flag[log_errors] = on

; Security settings
php_admin_value[disable_functions] = exec,passthru,shell_exec,system
php_admin_value[open_basedir] = /var/www:/tmp

; Performance settings
php_admin_value[memory_limit] = 128M
php_admin_value[max_execution_time] = 30
php_admin_value[post_max_size] = 32M
php_admin_value[upload_max_filesize] = 32M

; Session settings
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
