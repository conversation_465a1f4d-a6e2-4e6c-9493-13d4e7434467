<?php
// Sample PHP website homepage
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Website</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PHP Website is Running!</h1>
        
        <div class="info-box success">
            <strong>Success!</strong> PHP-FPM and Nginx are working together properly.
        </div>

        <h2>Server Information</h2>
        <table>
            <tr>
                <th>PHP Version</th>
                <td><?php echo PHP_VERSION; ?></td>
            </tr>
            <tr>
                <th>Server Software</th>
                <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
            </tr>
            <tr>
                <th>Document Root</th>
                <td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></td>
            </tr>
            <tr>
                <th>Server Name</th>
                <td><?php echo $_SERVER['SERVER_NAME'] ?? 'Unknown'; ?></td>
            </tr>
            <tr>
                <th>Request Time</th>
                <td><?php echo date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']); ?></td>
            </tr>
        </table>

        <h2>PHP Configuration</h2>
        <div class="info-box">
            <strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?><br>
            <strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s<br>
            <strong>Upload Max Filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?><br>
            <strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?>
        </div>

        <h2>Test Database Connection</h2>
        <?php
        // Test MongoDB connection if available
        if (class_exists('MongoDB\Client')) {
            try {
                $client = new MongoDB\Client("mongodb://localhost:27017");
                $db = $client->bookstore;
                echo '<div class="info-box success">✅ MongoDB connection successful!</div>';
            } catch (Exception $e) {
                echo '<div class="info-box">❌ MongoDB connection failed: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="info-box">ℹ️ MongoDB PHP driver not installed</div>';
        }
        ?>

        <h2>Sample Form</h2>
        <form method="POST" action="">
            <p>
                <label>Name: <input type="text" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"></label>
            </p>
            <p>
                <label>Message: <textarea name="message"><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea></label>
            </p>
            <p>
                <button type="submit">Submit</button>
            </p>
        </form>

        <?php if ($_POST): ?>
        <div class="info-box success">
            <h3>Form Submitted:</h3>
            <strong>Name:</strong> <?php echo htmlspecialchars($_POST['name'] ?? ''); ?><br>
            <strong>Message:</strong> <?php echo htmlspecialchars($_POST['message'] ?? ''); ?>
        </div>
        <?php endif; ?>

        <h2>Quick Links</h2>
        <ul>
            <li><a href="phpinfo.php">PHP Info</a></li>
            <li><a href="test.php">Test Page</a></li>
        </ul>
    </div>
</body>
</html>
