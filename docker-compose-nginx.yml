version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: my-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./:/var/www/html:ro
      - nginx_logs:/var/log/nginx
    networks:
      - nginx-network
    # Run as specific user
    user: "1000:1000"
    command: ["nginx", "-g", "daemon off;"]
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  nginx_logs:
    driver: local

networks:
  nginx-network:
    driver: bridge
