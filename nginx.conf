    # Main PHP website server block
    server {
        listen 80;
        server_name lei.com www.lei.com;
        root /home/<USER>/workspace;
        index index.php index.html index.htm;


        # Main location - supports clean URLs for PHP frameworks
        location / {
            try_files $uri $uri/ /index.php$is_args$args;
        }

        # PHP file processing with enhanced security
        location ~ \.php$ {
            # Security: Don't execute PHP in uploads/user directories
            location ~ ^/(uploads|files|media|user-content)/.*\.php$ {
                deny all;
            }

            try_files $uri =404;  # This line is crucial!
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass unix:/run/php/php8.3-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;

            # Timeouts for PHP processing
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;
            fastcgi_connect_timeout 60;

            # Buffer settings for better performance
            fastcgi_buffer_size 128k;
            fastcgi_buffers 4 256k;
            fastcgi_busy_buffers_size 256k;


        }

        # Static assets with aggressive caching
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot|otf)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
            try_files $uri =404;

            # Optional: serve pre-compressed files
            location ~* \.(css|js)$ {
                gzip_static on;
            }
        }

        # Handle favicon requests
        location = /favicon.ico {
            log_not_found off;
            access_log off;
            expires 1y;
        }

        # Handle robots.txt
        location = /robots.txt {
            log_not_found off;
            access_log off;
        }

        # Deny access to hidden files and directories
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Deny access to sensitive files
        location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|orig|save|swo|swp|tmp)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Deny access to version control directories
        location ~ /\.(git|svn|hg|bzr) {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Deny access to composer files
        location ~* (composer\.(json|lock)|package\.json|yarn\.lock|\.env)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # PHP framework specific configurations
        # Laravel/Lumen public directory
        location ~ ^/(storage|bootstrap/cache) {
            deny all;
        }

        # WordPress specific (uncomment if using WordPress)
        # location ~ ^/wp-content/uploads/.*\.php$ {
        #     deny all;
        # }
        # location = /xmlrpc.php {
        #     deny all;
        # }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;

        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
