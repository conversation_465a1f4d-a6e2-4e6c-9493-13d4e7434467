# PHP Website Configuration

This configuration provides a production-ready setup for running PHP websites with Nginx and PHP-FPM.

## Files Overview

- `nginx.conf` - Nginx configuration optimized for PHP websites
- `php-fpm.conf` - PHP-FPM pool configuration
- `docker-compose-php-website.yml` - Docker setup for the complete stack
- `index.php` - Sample PHP homepage
- `phpinfo.php` - PHP configuration info page

## Features

### Nginx Configuration
- ✅ **Security headers** (XSS protection, content type sniffing prevention)
- ✅ **Clean URLs** support for PHP frameworks
- ✅ **Static file caching** with 1-year expiration
- ✅ **Gzip compression** for better performance
- ✅ **Security restrictions** (deny access to sensitive files)
- ✅ **PHP framework support** (Laravel, WordPress ready)
- ✅ **Upload directory protection** (prevents PHP execution in uploads)

### PHP-FPM Configuration
- ✅ **Dynamic process management** (auto-scaling workers)
- ✅ **Security hardening** (disabled dangerous functions)
- ✅ **Performance optimization** (OPcache enabled)
- ✅ **Resource limits** (256MB memory, 64MB uploads)
- ✅ **Slow query logging** for debugging
- ✅ **Session security** (httponly cookies, strict mode)

## Quick Start

### Using Docker (Recommended)

```bash
# Start the PHP website
docker-compose -f docker-compose-php-website.yml up -d

# View logs
docker-compose -f docker-compose-php-website.yml logs -f

# Stop the website
docker-compose -f docker-compose-php-website.yml down
```

Visit: http://localhost:8080

### Manual Setup

1. **Install dependencies:**
   ```bash
   # Ubuntu/Debian
   sudo apt install nginx php8.3-fpm

   # CentOS/RHEL
   sudo yum install nginx php-fpm
   ```

2. **Copy configurations:**
   ```bash
   sudo cp nginx.conf /etc/nginx/nginx.conf
   sudo cp php-fpm.conf /etc/php/8.3/fpm/pool.d/www.conf
   ```

3. **Start services:**
   ```bash
   sudo systemctl start nginx php8.3-fpm
   sudo systemctl enable nginx php8.3-fpm
   ```

## Configuration Details

### Security Features
- Blocks execution of PHP files in upload directories
- Denies access to sensitive files (.env, composer.json, etc.)
- Prevents access to version control directories
- Disables dangerous PHP functions
- Implements security headers

### Performance Optimizations
- OPcache enabled with optimized settings
- Static file caching with proper headers
- Gzip compression for text files
- Efficient FastCGI buffering
- Dynamic process management

### Framework Support
- **Laravel/Lumen**: Clean URLs and directory protection
- **WordPress**: Upload protection and xmlrpc blocking (commented)
- **Generic PHP**: Flexible routing with fallback to index.php

## Customization

### For Different PHP Versions
Update the socket path in both files:
```nginx
# nginx.conf
server unix:/var/run/php/php8.2-fpm.sock;
```
```ini
# php-fpm.conf
listen = /var/run/php/php8.2-fpm.sock
```

### For Production
1. **Enable HTTPS:**
   ```nginx
   server {
       listen 443 ssl http2;
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
   }
   ```

2. **Adjust resource limits:**
   ```ini
   pm.max_children = 100
   php_admin_value[memory_limit] = 512M
   ```

3. **Enable security features:**
   ```ini
   php_value[session.cookie_secure] = 1  # For HTTPS
   ```

## Troubleshooting

### Common Issues

1. **502 Bad Gateway:**
   - Check PHP-FPM is running: `sudo systemctl status php8.3-fpm`
   - Verify socket permissions: `ls -la /var/run/php/`

2. **Permission Denied:**
   - Ensure www-data user exists
   - Check file ownership: `sudo chown -R www-data:www-data /var/www/html`

3. **Upload Issues:**
   - Check `upload_max_filesize` and `post_max_size` in php-fpm.conf
   - Verify `client_max_body_size` in nginx.conf

### Useful Commands

```bash
# Test nginx configuration
nginx -t

# Reload nginx
nginx -s reload

# Check PHP-FPM status
php-fpm8.3 -t

# View error logs
tail -f /var/log/nginx/error.log
tail -f /var/log/php-fpm/www-error.log
```

## Next Steps

1. Add your PHP application files to the current directory
2. Configure database connections in your PHP code
3. Set up SSL certificates for production
4. Configure backup and monitoring
5. Implement caching (Redis/Memcached) if needed

This configuration is production-ready and follows security best practices for PHP websites.
